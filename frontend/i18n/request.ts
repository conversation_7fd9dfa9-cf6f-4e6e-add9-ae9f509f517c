import { getRequestConfig } from "next-intl/server";
import { routing } from "./routing";

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  let locale = await requestLocale;

  // Normalize locale variants
  if (locale?.startsWith("zh")) {
    locale = "zh";
  } else if (locale?.startsWith("ja")) {
    locale = "ja";
  } else if (locale && !routing.locales.includes(locale as any)) {
    locale = routing.defaultLocale;
  } else if (!locale) {
    locale = routing.defaultLocale;
  }

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default,
  };
});
