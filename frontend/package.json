{"name": "codeocr", "version": "1.0.0", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@icons-pack/react-simple-icons": "^13.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-use-controllable-state": "^1.2.2", "@shikijs/transformers": "^3.4.2", "@types/js-cookie": "^3.0.6", "antd": "^5.25.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.468.0", "next": "15.2.4", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "shiki": "^3.4.2", "sonner": "^2.0.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zustand": "^5.0.3"}, "devDependencies": {"@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-next": "^0.4.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.0.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.3", "typescript": "^5"}}